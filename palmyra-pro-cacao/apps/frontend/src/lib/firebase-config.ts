// Firebase configuration for different environments
export interface FirebaseConfig {
  apiKey: string
  authDomain: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
}

export interface EnvironmentConfig {
  firebase: FirebaseConfig
  backendUrl: string
  environment: 'development' | 'staging' | 'production' | 'preview'
}

// Firebase configurations for each environment
const firebaseConfigs: Record<string, FirebaseConfig> = {
  // Development environment (cacao--dev with two dashes)
  development: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY_DEV || process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN_DEV || 'cacao--dev.firebaseapp.com',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID_DEV || 'cacao--dev',
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET_DEV || 'cacao--dev.firebasestorage.app',
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID_DEV || process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID_DEV || process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
  },
  
  // Staging environment (also uses cacao--dev but different hosting)
  staging: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY_STAGING || process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN_STAGING || 'cacao--dev.firebaseapp.com',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID_STAGING || 'cacao--dev',
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET_STAGING || 'cacao--dev.firebasestorage.app',
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID_STAGING || process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID_STAGING || process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
  },
  
  // Production environment
  production: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY_PROD || '',
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN_PROD || 'cacao-prod.firebaseapp.com',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID_PROD || 'cacao-prod',
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET_PROD || 'cacao-prod.firebasestorage.app',
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID_PROD || '',
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID_PROD || '',
  },
  
  // Preview/PR environments (uses dev config)
  preview: {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY_DEV || process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN_DEV || 'cacao--dev.firebaseapp.com',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID_DEV || 'cacao--dev',
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET_DEV || 'cacao--dev.firebasestorage.app',
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID_DEV || process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID_DEV || process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
  },
}

/**
 * Detects the current environment based on URL and environment variables
 */
export function detectEnvironment(): 'development' | 'staging' | 'production' | 'preview' {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    const origin = window.location.origin
    
    // Production environment
    if (hostname === 'cacao.palmyra.pro') {
      return 'production'
    }
    
    // Staging environment
    if (hostname === 'cacao---dev.web.app' || origin.includes('cacao---dev.web.app')) {
      return 'staging'
    }
    
    // Preview/PR environments (Firebase hosting preview channels)
    if (hostname.includes('cacao--dev--') || origin.includes('cacao--dev--')) {
      return 'preview'
    }
    
    // Development (localhost or other dev URLs)
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost')) {
      return 'development'
    }
    
    // Default to preview for any other Firebase hosting URLs
    if (hostname.includes('firebaseapp.com') || hostname.includes('web.app')) {
      return 'preview'
    }
  }
  
  // Server-side or fallback detection
  const nodeEnv = process.env.NODE_ENV
  const envShortName = process.env.NEXT_PUBLIC_ENV_SHORT_NAME || process.env.ENV_SHORT_NAME
  
  if (envShortName === 'prod' || nodeEnv === 'production') {
    return 'production'
  }
  
  if (envShortName === 'staging') {
    return 'staging'
  }
  
  // Default to development
  return 'development'
}

/**
 * Gets the Firebase configuration for the current environment
 */
export function getFirebaseConfig(): FirebaseConfig {
  const environment = detectEnvironment()
  const config = firebaseConfigs[environment]
  
  if (!config) {
    console.warn(`No Firebase config found for environment: ${environment}, falling back to development`)
    return firebaseConfigs.development
  }
  
  // Validate that required fields are present
  if (!config.apiKey || !config.projectId) {
    console.error(`Invalid Firebase config for environment: ${environment}`, config)
    throw new Error(`Firebase configuration is incomplete for environment: ${environment}`)
  }
  
  return config
}

/**
 * Gets the complete environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = detectEnvironment()
  const firebase = getFirebaseConfig()
  
  // Determine backend URL based on environment
  let backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || ''
  
  if (!backendUrl && typeof window !== 'undefined') {
    // If no explicit backend URL is set, try to infer it from the current environment
    const hostname = window.location.hostname
    
    if (hostname === 'cacao.palmyra.pro') {
      // Production backend
      backendUrl = 'https://prod-cacao-api-[PROJECT_NUMBER].[REGION].run.app'
    } else if (hostname === 'cacao---dev.web.app') {
      // Staging backend
      backendUrl = 'https://staging-cacao-api-[PROJECT_NUMBER].[REGION].run.app'
    } else if (hostname.includes('cacao--dev--')) {
      // Preview/PR backend - extract the PR identifier
      const match = hostname.match(/cacao--dev--([^.]+)/)
      if (match) {
        const prId = match[1]
        backendUrl = `https://${prId}-cacao-api-[PROJECT_NUMBER].[REGION].run.app`
      }
    }
  }
  
  return {
    firebase,
    backendUrl,
    environment,
  }
}

/**
 * Debug function to log current configuration
 */
export function debugConfiguration() {
  if (process.env.NODE_ENV === 'development') {
    const config = getEnvironmentConfig()
    console.log('🔧 Firebase Environment Configuration:', {
      environment: config.environment,
      projectId: config.firebase.projectId,
      authDomain: config.firebase.authDomain,
      backendUrl: config.backendUrl,
      hostname: typeof window !== 'undefined' ? window.location.hostname : 'server-side',
    })
  }
}
