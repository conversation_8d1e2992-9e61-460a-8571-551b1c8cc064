// Import the functions you need from the SDKs you need
import { initializeApp, FirebaseApp } from "firebase/app"
import { getAuth, Auth } from "firebase/auth"
import { getFirebaseConfig, debugConfiguration } from "./firebase-config"

// Initialize Firebase with dynamic configuration
let app: FirebaseApp
let auth: Auth

try {
  const firebaseConfig = getFirebaseConfig()

  // Debug configuration in development
  debugConfiguration()

  // Initialize Firebase
  app = initializeApp(firebaseConfig)
  auth = getAuth(app)

  console.log(`🔥 Firebase initialized for project: ${firebaseConfig.projectId}`)
} catch (error) {
  console.error('❌ Failed to initialize Firebase:', error)
  throw error
}

export { auth, app }
