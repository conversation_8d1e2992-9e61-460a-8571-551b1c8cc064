# Reusable workflow for deploying applications to GCP and Firebase
# This workflow handles both frontend (Firebase Hosting) and backend (Cloud Run) deployments
#
# Required inputs:
#   - environment: GitHub environment to use for deployment
#   - short-name: Short name used as prefix for resources, labels, etc.
#   - tag-name: Docker image tag to deploy
#   - channel-id: Firebase hosting channel ID (live or preview channel name)
#   - fe-url: Frontend URL for CORS configuration
#   - api-url: Backend API URL
#   - db-name: Database name
#   - db-host: Database host
#   - min-instances: Minimum Cloud Run instances
#   - gcp-db-password-secret-name: GCP Secret Manager secret name for DB password
#   - db-instance-connection-name: Cloud SQL instance connection name
#   - gcp-project-id: GCP Project ID
#   - gcp-region: GCP Region
#   - db-port: Database port
#   - db-user: Database user
#   - db-ssl-mode: Database SSL mode
#   - metabase-site-url: Metabase site URL
#   - firebase-auth-secret: Firebase authentication secret
#
# Required secrets:
#   - GCP_SERVICE_ACCOUNT_KEY: Service account credentials for GCP authentication
#
# Features:
#   - Firebase Hosting deployment (live or preview channels)
#   - Cloud Run backend deployment with Cloud SQL integration
#   - Automatic CORS configuration between frontend and backend
#   - PR comment with deployment URLs
#   - Comprehensive deployment summary

name: Deploy Applications
run-name: Deploy ${{ inputs.short-name || github.sha }}

on:
  workflow_call:
    inputs:
      environment:
        description: "GitHub environment to use for deployment"
        required: true
        type: string
      short-name:
        description: "Short name used as prefix for resources, labels, etc."
        required: true
        type: string
      tag-name:
        description: "Docker image tag to deploy"
        required: true
        type: string
      channel-id:
        description: "Firebase hosting channel ID (live or preview channel name)"
        required: true
        type: string
      fe-url:
        description: "Frontend URL for CORS configuration"
        required: true
        type: string
      api-url:
        description: "Backend API URL"
        required: true
        type: string
      db-name:
        description: "Database name"
        required: true
        type: string
      db-host:
        description: "Database host"
        required: true
        type: string
      min-instances:
        description: "Minimum Cloud Run instances"
        required: true
        type: string
      gcp-db-password-secret-name:
        description: "GCP Secret Manager secret name for DB password"
        required: true
        type: string
      db-instance-connection-name:
        description: "Cloud SQL instance connection name"
        required: true
        type: string
      gcp-project-id:
        description: "GCP Project ID"
        required: true
        type: string
      gcp-region:
        description: "GCP Region"
        required: true
        type: string
      db-port:
        description: "Database port"
        required: true
        type: string
      db-user:
        description: "Database user"
        required: true
        type: string
      db-ssl-mode:
        description: "Database SSL mode"
        required: true
        type: string
      metabase-site-url:
        description: "Metabase site URL"
        required: true
        type: string
      firebase-auth-secret:
        description: "Firebase authentication secret"
        required: true
        type: string
    secrets:
      GCP_SERVICE_ACCOUNT_KEY:
        description: "Service account credentials for GCP authentication"
        required: true

    outputs:
      frontend-url:
        description: "Deployed frontend URL"
        value: ${{ jobs.deploy.outputs.frontend-url }}
      backend-url:
        description: "Deployed backend URL"
        value: ${{ inputs.api-url }}
      deployment-summary:
        description: "Deployment summary"
        value: ${{ jobs.deploy.outputs.deployment-summary }}

jobs:
  deploy:
    name: "Deploy Applications"
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    env:
      SHORT_NAME: ${{ inputs.short-name }}
      TAG_NAME: ${{ inputs.tag-name }}
      CHANNEL_ID: ${{ inputs.channel-id }}
      FE_URL: ${{ inputs.fe-url }}
      API_URL: ${{ inputs.api-url }}
      DB_NAME: ${{ inputs.db-name }}
      DB_HOST: ${{ inputs.db-host }}
      MIN_INSTANCES: ${{ inputs.min-instances }}
      GCP_DB_PASSWORD_SECRET_NAME: ${{ inputs.gcp-db-password-secret-name }}
      DB_INSTANCE_CONNECTION_NAME: ${{ inputs.db-instance-connection-name }}

    outputs:
      frontend-url: ${{ steps.deploy-frontend.outputs.PREVIEW_URL || inputs.fe-url }}
      deployment-summary: ${{ steps.deployment-summary.outputs.summary }}

    steps:
      - name: Debug Input Values
        run: |
          echo " Debug: Input values received:"
          echo "  gcp-project-id: '${{ inputs.gcp-project-id }}'"
          echo "  gcp-region: '${{ inputs.gcp-region }}'"
          echo "  short-name: '${{ inputs.short-name }}'"
          echo "  tag-name: '${{ inputs.tag-name }}'"
          echo "  db-port: '${{ inputs.db-port }}'"
          echo "  db-user: '${{ inputs.db-user }}'"
          echo "  db-ssl-mode: '${{ inputs.db-ssl-mode }}'"

      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Download Frontend Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: apps/frontend/dist/

      - name: Deploy to Firebase Hosting
        id: deploy-frontend
        working-directory: apps/frontend
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          npm install -g firebase-tools

          # Deploy
          TMP_OUTPUT=$(mktemp)

          EXIT_CODE=0
          if [ "$CHANNEL_ID" == "live" ]; then
            echo "Deploying to live channel..."
            firebase deploy \
            --only hosting \
            --project="${{ inputs.gcp-project-id }}" \
            --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL="https://${{ inputs.gcp-project-id }}.web.app/"

            echo "✅ Frontend deployed to: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

          else
            echo "Deploying to preview channel: $CHANNEL_ID"
            firebase hosting:channel:deploy "$CHANNEL_ID" \
              --project="${{ inputs.gcp-project-id }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Frontend deployed to: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
          fi

          rm "$TMP_OUTPUT"

      - name: Deploy to Cloud Run
        id: deploy-backend
        run: |
          echo "Deploying backend to Cloud Run..."

          # Use the frontend URL from the previous step if it was updated
          ALLOWED_ORIGIN="${{ env.FE_URL }}"
          if [ -n "${{ steps.deploy-frontend.outputs.PREVIEW_URL }}" ]; then
            ALLOWED_ORIGIN="${{ steps.deploy-frontend.outputs.PREVIEW_URL }}"
          fi

          # Deploy with volume mount for Firebase secret
          gcloud run deploy $SHORT_NAME-cacao-api \
            --image ${{ inputs.gcp-region }}-docker.pkg.dev/${{ inputs.gcp-project-id }}/palmyra-pro-images/cacao-api:$TAG_NAME \
            --region ${{ inputs.gcp-region }} \
            --project ${{ inputs.gcp-project-id }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }},DB_HOST=${{ env.DB_HOST }},DB_PORT=${{ inputs.db-port }},DB_USER=${{ inputs.db-user }},DB_SSL_MODE=${{ inputs.db-ssl-mode }}" \
            --set-env-vars "ALLOWED_ORIGIN=${ALLOWED_ORIGIN},METABASE_SITE_URL=${{ inputs.metabase-site-url }},ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --set-env-vars "FIREBASE_ADMIN_KEY_PATH=/secrets/FIREBASE_SERVICE_ACCOUNT_KEY,FIREBASE_AUTH_SECRET=${{ inputs.firebase-auth-secret }},FE_URL=${ALLOWED_ORIGIN}" \
            --set-secrets "/secrets/FIREBASE_SERVICE_ACCOUNT_KEY=FIREBASE_SERVICE_ACCOUNT_KEY:latest,DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest,METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ env.SHORT_NAME }}"

          # Add IAM policy binding for public access
          gcloud run services add-iam-policy-binding $SHORT_NAME-cacao-api \
            --region ${{ inputs.gcp-region }} \
            --project ${{ inputs.gcp-project-id }} \
            --member="allUsers" \
            --role="roles/run.invoker"

          echo "✅ Backend deployed successfully to Cloud Run"
          echo "Service URL: ${{ env.API_URL }}"

      - name: Comment on PR with deployment URLs
        if: github.event_name == 'pull_request'
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ## 🚀 Environment Deployed Successfully

            **Frontend:** ${{ steps.deploy-frontend.outputs.PREVIEW_URL || inputs.fe-url }}
            **Backend API:** ${{ inputs.api-url }}
            **Database:** ${{ inputs.db-name }}

            Environment: `${{ inputs.short-name }}`

      - name: Deployment Summary
        id: deployment-summary
        run: |
          echo "🎉 Deployment completed successfully!"
          echo "===============xxx==================="
          echo "Environment: ${{ inputs.short-name }}"
          FRONTEND_URL="${{ steps.deploy-frontend.outputs.PREVIEW_URL }}"
          if [ -z "$FRONTEND_URL" ]; then
            FRONTEND_URL="${{ inputs.fe-url }}"
          fi
          echo "Frontend URL: $FRONTEND_URL"
          echo "Backend API: ${{ inputs.api-url }}"
          echo "Database: ${{ inputs.db-name }}"
          echo "================xxx=================="
          
          # Set output for summary
          SUMMARY="Environment: ${{ inputs.short-name }}, Frontend: $FRONTEND_URL, Backend: ${{ inputs.api-url }}, Database: ${{ inputs.db-name }}"
          echo "summary=$SUMMARY" >> "$GITHUB_OUTPUT"
